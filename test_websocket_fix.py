#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
测试WebSocket修复的简单脚本
"""

import asyncio
import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kzmfunction.WebSocketAdapter.WebSocketCore import create_wss_adapter

def test_callback(adapter, data):
    """测试回调函数"""
    print(f"收到数据: {data}")

async def test_binance_spot_bonk():
    """测试Binance现货BONK连接"""
    print("测试Binance现货BONK WebSocket连接...")
    
    try:
        # 创建WebSocket适配器
        adapter = create_wss_adapter(
            exchange_name='binance',
            symbol='bonk',
            on_message_callback=test_callback,
            market_type='spot'
        )
        
        print(f"创建的适配器: {adapter}")
        print(f"适配器的symbol: {adapter._symbol}")
        
        # 启动连接
        await adapter.start()
        
        # 等待一段时间接收数据
        await asyncio.sleep(10)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'adapter' in locals():
            await adapter.stop()

async def test_binance_swap_bonk():
    """测试Binance合约BONK连接"""
    print("测试Binance合约BONK WebSocket连接...")
    
    try:
        # 创建WebSocket适配器
        adapter = create_wss_adapter(
            exchange_name='binance',
            symbol='bonk',
            on_message_callback=test_callback,
            market_type='swap'
        )
        
        print(f"创建的适配器: {adapter}")
        print(f"适配器的symbol: {adapter._symbol}")
        
        # 启动连接
        await adapter.start()
        
        # 等待一段时间接收数据
        await asyncio.sleep(10)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'adapter' in locals():
            await adapter.stop()

async def main():
    """主函数"""
    print("开始测试WebSocket修复...")
    
    # 测试现货
    await test_binance_spot_bonk()
    
    print("\n" + "="*50 + "\n")
    
    # 测试合约
    await test_binance_swap_bonk()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
