#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
测试套利程序数据接收的简化脚本
"""

import asyncio
import sys
import os
import time

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kzmfunction.WebSocketAdapter.WebSocketCore import (
    WSConfig,
    OrderbookCache,
    WebSocketManager,
    create_wss_adapter
)
from kzmfunction.WebSocketAdapter.CallbackHandlers import (
    create_callback_handler
)

# 全局变量
orderbook_cache = OrderbookCache()
ws_manager = None
callback_handler = None
data_received = {'binance': False, 'hyperliquid': False}

def get_trading_enabled():
    """获取交易启用状态"""
    return True

async def trigger_trading_logic_wrapper(exchange_1_data, exchange_2_data):
    """交易逻辑包装器"""
    try:
        print("="*50)
        print("收到套利数据:")
        
        # 获取价格数据（使用修复后的逻辑）
        if 'binance' in [exchange_1_name, exchange_2_name] and 'hyperliquid' in [exchange_1_name, exchange_2_name]:
            if exchange_1_name == 'binance':
                exchange_1_sell1_price = float(exchange_1_data['ask'][0]) * 1000  # 现货卖1价格 (转换为kcoin单位)
                exchange_1_sell1_amount = float(exchange_1_data['ask'][1]) / 1000  # 现货卖1数量 (转换为kcoin单位)
            else:
                exchange_1_sell1_price = float(exchange_1_data['ask'][0])
                exchange_1_sell1_amount = float(exchange_1_data['ask'][1])
        else:
            exchange_1_sell1_price = float(exchange_1_data['ask'][0])
            exchange_1_sell1_amount = float(exchange_1_data['ask'][1])
        
        exchange_2_buy1_price = float(exchange_2_data['bid'][0])
        exchange_2_buy1_amount = float(exchange_2_data['bid'][1])
        
        # 计算价差
        r = exchange_2_buy1_price / exchange_1_sell1_price - 1
        
        print(f"{exchange_1_name} 现货价格: {exchange_1_sell1_price:.6f}, 数量: {exchange_1_sell1_amount:.2f}")
        print(f"{exchange_2_name} 合约价格: {exchange_2_buy1_price:.6f}, 数量: {exchange_2_buy1_amount:.2f}")
        print(f"价差: {r:.4f} ({r*100:.2f}%)")
        
        if r > 0:
            print("✅ 正价差 - 可以套利")
        else:
            print("❌ 负价差 - 不适合套利")
            
    except Exception as e:
        print(f"处理套利数据错误: {e}")
        import traceback
        traceback.print_exc()

async def setup_websockets():
    """设置WebSocket连接"""
    global callback_handler, ws_manager

    try:
        # WebSocket配置
        ws_config = WSConfig(
            reconnect_interval=5.0,
            max_reconnect_attempts=10,
            ping_interval=30.0,
            data_timeout=60.0
        )
        
        # WebSocket管理器
        ws_manager = WebSocketManager(ws_config)

        # 初始化回调处理器
        callback_handler = create_callback_handler(
            'arbitrage',
            orderbook_cache,
            exchange_1_name=exchange_1_name,
            exchange_2_name=exchange_2_name,
            symbol=symbol,
            r_threshold=0.001,  # 很小的阈值，便于测试
            trading_enabled_callback=get_trading_enabled,
            trigger_trading_callback=trigger_trading_logic_wrapper,
            trade_cooldown=2.0  # 2秒冷却时间
        )

        # 创建WebSocket适配器
        exchange_1_wss = create_wss_adapter(
            exchange_1_name,
            symbol,
            callback_handler.handle_orderbook_update,
            market_type='spot'
        )

        exchange_2_wss = create_wss_adapter(
            exchange_2_name,
            symbol,
            callback_handler.handle_orderbook_update,
            market_type='swap'
        )

        if not exchange_1_wss or not exchange_2_wss:
            raise Exception(f"无法创建WebSocket适配器: {exchange_1_name}={exchange_1_wss}, {exchange_2_name}={exchange_2_wss}")

        # 添加到WebSocket管理器
        await ws_manager.add_adapter(f"{exchange_1_name}_spot", exchange_1_wss)
        await ws_manager.add_adapter(f"{exchange_2_name}_swap", exchange_2_wss)

        print(f"WebSocket连接已设置:")
        print(f"  {exchange_1_name} 现货: {symbol}")
        print(f"  {exchange_2_name} 合约: {symbol}")

    except Exception as e:
        print(f"WebSocket设置失败: {e}")
        import traceback
        traceback.print_exc()
        raise

async def main():
    """主函数"""
    print("开始测试套利数据接收...")
    print(f"交易对: {symbol}")
    print(f"Exchange 1 (现货): {exchange_1_name}")
    print(f"Exchange 2 (合约): {exchange_2_name}")
    print("="*50)
    
    try:
        # 设置WebSocket
        await setup_websockets()
        
        # 启动WebSocket
        await ws_manager.start_all()
        print("所有WebSocket连接已启动")
        
        # 等待数据
        print("等待数据接收...")
        await asyncio.sleep(30)  # 等待30秒
        
    except KeyboardInterrupt:
        print("收到中断信号，正在停止...")
    except Exception as e:
        print(f"主程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if ws_manager:
            await ws_manager.stop_all()
            print("所有WebSocket连接已停止")

# 配置
symbol = 'bonk'
exchange_1_name = 'binance'  # 现货
exchange_2_name = 'hyperliquid'  # 合约

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序异常: {e}")
        import traceback
        traceback.print_exc()
