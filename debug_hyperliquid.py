#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
调试Hyperliquid WebSocket消息格式
"""

import asyncio
import json
import websockets

async def test_hyperliquid_websocket():
    """测试Hyperliquid WebSocket连接和消息格式"""
    uri = "wss://api.hyperliquid.xyz/ws"
    
    try:
        print(f"连接到 {uri}...")
        async with websockets.connect(uri) as websocket:
            print("连接成功!")
            
            # 订阅kBONK的最佳买卖价
            subscribe_message = {
                'method': 'subscribe',
                'subscription': {
                    'type': 'bbo',
                    'coin': 'kBONK'
                }
            }
            
            await websocket.send(json.dumps(subscribe_message))
            print(f"发送订阅消息: {subscribe_message}")
            
            # 接收消息
            message_count = 0
            async for message in websocket:
                try:
                    payload = json.loads(message)
                    message_count += 1
                    
                    print(f"\n=== 消息 {message_count} ===")
                    print(f"原始消息: {message}")
                    print(f"解析后: {json.dumps(payload, indent=2)}")
                    
                    # 分析消息结构
                    if 'method' in payload:
                        print(f"消息类型: {payload.get('method')}")
                    if 'channel' in payload:
                        print(f"频道: {payload.get('channel')}")
                    if 'data' in payload:
                        print(f"数据: {payload.get('data')}")
                    
                    # 只接收前10条消息
                    if message_count >= 10:
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"原始消息: {message}")
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    
    except Exception as e:
        print(f"连接错误: {e}")

if __name__ == '__main__':
    asyncio.run(test_hyperliquid_websocket())
